defmodule RepobotWeb.WebhookController.RepositoryDeletedTest do
  use RepobotWeb.ConnCase, async: false
  use Oban.Testing, repo: Repobot.Repo
  use Repobot.Test.Fixtures

  alias Repobot.Events
  alias Repobot.Repositories
  alias Repobot.Workers.EventHandlers.GitHub.RepositoryDeleted

  import Mox
  setup :verify_on_exit!

  setup do
    # Set up Oban test mode
    :ok = Oban.Testing.with_testing_mode(:inline, fn -> :ok end)

    # Setup signature verifier mock
    Repobot.Test.SignatureVerifierMock
    |> stub(:verify_signature, fn _conn -> :ok end)

    user = create_user()
    organization = user.default_organization

    repository_data = %{
      "id" => 123_456_789,
      "name" => "test-repo",
      "full_name" => "testorg/test-repo",
      "owner" => %{"login" => "testorg"},
      "private" => false,
      "description" => "Test repository",
      "language" => "Elixir",
      "fork" => false
    }

    repository =
      create_repository(%{
        organization_id: organization.id,
        name: "test-repo",
        full_name: "testorg/test-repo",
        owner: "testorg",
        data: repository_data
      })

    %{user: user, organization: organization, repository: repository}
  end

  describe "repository deleted webhook" do
    test "creates event and schedules worker for repository deletion", %{
      conn: conn,
      repository: repository
    } do
      payload = %{
        "action" => "deleted",
        "repository" => %{
          "id" => repository.data["id"],
          "name" => "test-repo",
          "full_name" => "testorg/test-repo",
          "owner" => %{"login" => "testorg"},
          "private" => false,
          "description" => "Test repository",
          "language" => "Elixir",
          "fork" => false
        }
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Verify repository was deleted by the worker (worker runs inline)
      assert_raise Ecto.NoResultsError, fn ->
        Repositories.get_repository!(repository.id)
      end
    end

    test "handles repository not found in database", %{conn: conn} do
      payload = %{
        "action" => "deleted",
        "repository" => %{
          # Non-existent repository
          "id" => 999_999_999,
          "name" => "test-repo",
          "full_name" => "testorg/test-repo",
          "owner" => %{"login" => "testorg"},
          "private" => false,
          "description" => "Test repository",
          "language" => "Elixir",
          "fork" => false
        }
      }

      conn =
        conn
        |> put_req_header("x-github-event", "repository")

      response = post(conn, ~p"/hooks/", payload)

      assert json_response(response, 200) == %{"status" => "ok"}

      # Test passes if webhook doesn't crash - no event should be created for non-existent repository
    end

    test "worker handles missing event", %{repository: _repository} do
      # Create a job with non-existent event_id (using a valid binary_id format)
      non_existent_id = Ecto.UUID.generate()

      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryDeleted",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => non_existent_id}
      }

      result = RepositoryDeleted.perform(job)
      assert {:error, error_message} = result
      assert error_message == "Event not found: #{non_existent_id}"
    end

    test "worker handles invalid job arguments", %{repository: _repository} do
      # Create a job without event_id
      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryDeleted",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"invalid" => "args"}
      }

      result = RepositoryDeleted.perform(job)
      assert {:error, "Missing event_id in job arguments"} = result
    end

    test "worker handles repository not found during processing", %{
      organization: organization
    } do
      # Create an event for a non-existent repository
      payload = %{
        "action" => "deleted",
        "repository" => %{
          "id" => 999_999_999,
          "name" => "non-existent-repo",
          "full_name" => "testorg/non-existent-repo",
          "owner" => %{"login" => "testorg"},
          "private" => false,
          "description" => "Non-existent repository"
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.repository.deleted",
          payload,
          organization.id,
          nil,
          nil
        )

      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryDeleted",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      result = RepositoryDeleted.perform(job)
      assert :ok = result

      # Verify event status was updated to completed (since repository not found is handled gracefully)
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"
    end

    test "worker processes repository deletion successfully and sends notification", %{
      repository: repository
    } do
      # Create an event for repository deletion
      payload = %{
        "action" => "deleted",
        "repository" => %{
          "id" => repository.data["id"],
          "name" => repository.name,
          "full_name" => repository.full_name,
          "owner" => %{"login" => repository.owner},
          "private" => false,
          "description" => "Test repository"
        }
      }

      {:ok, event} =
        Events.log_event(
          "github.repository.deleted",
          payload,
          repository.organization_id,
          nil,
          repository.id
        )

      job = %Oban.Job{
        id: 1,
        worker: "Repobot.Workers.EventHandlers.GitHub.RepositoryDeleted",
        queue: "default",
        attempt: 1,
        max_attempts: 3,
        args: %{"event_id" => event.id}
      }

      result = RepositoryDeleted.perform(job)
      assert :ok = result

      # Verify repository was deleted
      assert_raise Ecto.NoResultsError, fn ->
        Repositories.get_repository!(repository.id)
      end

      # Verify event was deleted as part of repository cleanup
      assert Events.get_event(event.id) == nil
    end
  end
end
